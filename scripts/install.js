const fs = require("fs");
const { execSync } = require("child_process");

const IS_CI = <PERSON><PERSON><PERSON>(process.env.KCI_PIPELINE_ID);

/**
 * downloadSqliteBinary
 */
async function downloadSqliteBinary() {
  const curPlat=process.platform;
  const curArch=process.arch;
  const PLATFORMS = [
    `${curPlat}-${curArch}`,
    // "win32-x64",
    // "win32-arm64",
    // "linux-x64",
    // "linux-arm64",
    // "darwin-x64",
    // "darwin-arm64",
  ];
  // NOTE: 内网下载 ｜ github下载
  const url = "https://github.com/TryGhost/node-sqlite3/releases/download/v5.1.7/";
  const tarNames = {
    "darwin-arm64":
      "sqlite3-v5.1.7-napi-v6-darwin-arm64.tar.gz",
    "linux-arm64":
      "sqlite3-v5.1.7-napi-v3-linux-arm64.tar.gz",
    "win32-arm64":
      "sqlite3-v5.1.7-napi-v6-win32-ia32.tar.gz",
    "linux-x64":
      "sqlite3-v5.1.7-napi-v3-linux-x64.tar.gz",
    "darwin-x64":
      "sqlite3-v5.1.7-napi-v6-darwin-x64.tar.gz",
    "win32-x64":
      "sqlite3-v5.1.7-napi-v3-win32-x64.tar.gz",
  };
  PLATFORMS.forEach((platform) => {
    if (fs.existsSync(`./bin/sqlite3/${platform}/Release/node_sqlite3.node`)) {
      console.log(`sqlite3 for ${platform} already exists`);
      return;
    }
    const proxy = IS_CI ? "-x ***********:11080" : "";
    execCmdSync(`curl ${proxy} -L -o ./build.tar.gz ${url + tarNames[platform]}`);
    execCmdSync(`tar -xvzf build.tar.gz`);
    if (!fs.existsSync(`./bin/sqlite3/${platform}/Release`)) {
      fs.mkdirSync(`./bin/sqlite3/${platform}/Release`, { recursive: true });
    }
    fs.renameSync(`./build/Release/node_sqlite3.node`, `./bin/sqlite3/${platform}/Release/node_sqlite3.node`);
    fs.unlinkSync(`build.tar.gz`);
    execCmdSync(`rm -rf ./build`);
  });
}

function execCmdSync(cmd) {
  try {
    execSync(cmd, { stdio: "inherit" });
  }
  catch (e) {
    console.error(`exec cmd error: ${cmd}`);
    process.exit(1);
  }
}

(async () => {
  console.log("downloadSqliteBinary");
  await downloadSqliteBinary();
})();
