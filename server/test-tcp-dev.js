#!/usr/bin/env node

/**
 * TCP 开发模式测试脚本
 * 
 * 这个脚本模拟一个简单的 TCP 代理服务，用于测试开发环境下的 TCP 通信
 */

const net = require('net');

const PORT = process.env.KWAIPILOT_AGENT_PORT || 3000;
const HOST = '127.0.0.1';

console.log(`🚀 启动 TCP 代理服务测试服务器...`);
console.log(`📡 监听地址: ${HOST}:${PORT}`);

const server = net.createServer((socket) => {
  console.log(`\n🔗 客户端连接: ${socket.remoteAddress}:${socket.remotePort}`);
  
  let dataBuffer = '';
  
  socket.on('data', (data) => {
    console.log(`📨 收到原始数据: ${data.length} 字节`);
    
    dataBuffer += data.toString();
    
    // 处理可能的多行消息
    let newlineIndex;
    while ((newlineIndex = dataBuffer.indexOf('\n')) !== -1) {
      const line = dataBuffer.substring(0, newlineIndex).trim();
      dataBuffer = dataBuffer.substring(newlineIndex + 1);
      
      if (line) {
        handleMessage(socket, line);
      }
    }
  });
  
  socket.on('end', () => {
    console.log('🔌 客户端断开连接');
  });
  
  socket.on('error', (err) => {
    console.error('❌ Socket 错误:', err.message);
  });
});

function handleMessage(socket, line) {
  try {
    console.log(`📥 收到消息: ${line.slice(0, 100)}${line.length > 100 ? '...' : ''}`);
    
    const message = JSON.parse(line);
    console.log(`🔍 解析消息:`, {
      messageType: message.messageType,
      messageId: message.messageId,
      dataKeys: Object.keys(message.data || {}),
    });
    
    // 根据消息类型生成响应
    let responseData;
    
    switch (message.messageType) {
      case 'assistant/agent/local':
        responseData = handleAgentLocalMessage(message);
        break;
        
      case 'state/agentState':
        responseData = {
          status: 'ok',
          data: { isPaused: false }
        };
        break;
        
      case 'search/search':
        responseData = {
          status: 'ok',
          data: {
            results: [
              {
                file: 'example.js',
                content: '示例搜索结果',
                line: 1,
                score: 0.9
              }
            ],
            total: 1,
            query: message.data?.query || ''
          }
        };
        break;
        
      default:
        responseData = {
          status: 'ok',
          data: `模拟响应: ${message.messageType}`
        };
    }
    
    // 发送响应
    const response = {
      messageType: message.messageType,
      messageId: message.messageId,
      data: responseData
    };
    
    const responseJson = JSON.stringify(response) + '\n';
    socket.write(responseJson);
    
    console.log(`📤 发送响应: ${JSON.stringify(response).slice(0, 100)}${JSON.stringify(response).length > 100 ? '...' : ''}`);
    
  } catch (error) {
    console.error('❌ 处理消息失败:', error.message);
    console.error('📄 原始消息:', line);
    
    // 发送错误响应
    try {
      const errorResponse = {
        messageType: 'error',
        messageId: 'error-' + Date.now(),
        data: {
          status: 'error',
          message: error.message
        }
      };
      
      socket.write(JSON.stringify(errorResponse) + '\n');
    } catch (e) {
      console.error('❌ 发送错误响应失败:', e.message);
    }
  }
}

function handleAgentLocalMessage(message) {
  const data = message.data || {};

  console.log(`🤖 处理 AI 代理请求:`, {
    type: data.type,
    task: data.task?.slice(0, 50) + (data.task?.length > 50 ? '...' : ''),
    sessionId: data.reqData?.sessionId,
    chatId: data.reqData?.chatId,
    contextItemsCount: data.contextItems?.length || 0,
  });

  // 模拟 AI 响应
  const responses = [
    '你好！我是 DeepSearch AI 助手。我已经收到了你的消息，这是一个模拟响应。',
    '感谢你的问题！在开发模式下，我正在通过 TCP 连接与你通信。',
    '这是一个测试响应，用于验证 TCP 开发模式是否正常工作。',
    '我可以帮助你进行代码搜索、文件编辑和其他开发任务。',
    '当前运行在开发模式下，使用 TCP 通信协议。'
  ];

  const randomResponse = responses[Math.floor(Math.random() * responses.length)];
  const fullResponse = `${randomResponse}\n\n**调试信息:**\n- 消息类型: ${data.type}\n- 会话ID: ${data.reqData?.sessionId}\n- 聊天ID: ${data.reqData?.chatId}\n- 上下文项目数: ${data.contextItems?.length || 0}`;

  // 根据协议定义，assistant/agent/local 应该返回 ResponseBase<null> 格式
  // 实际的AI响应内容通过 assistant/agent/message 消息发送

  return {
    status: 'ok',
    message: 'Task started successfully',
    data: fullResponse, // 临时将响应内容放在这里，方便测试
    code: 200
  };
}

server.listen(PORT, HOST, () => {
  console.log(`✅ TCP 代理服务已启动`);
  console.log(`📍 地址: ${HOST}:${PORT}`);
  console.log(`\n💡 使用方法:`);
  console.log(`   1. 在另一个终端运行: NODE_ENV=development npm run start:dev`);
  console.log(`   2. 访问 http://localhost:3001 测试 Web 界面`);
  console.log(`   3. 发送消息测试 TCP 通信`);
  console.log(`\n🔧 环境变量:`);
  console.log(`   KWAIPILOT_AGENT_PORT=${PORT}`);
  console.log(`   NODE_ENV=development`);
  console.log(`\n按 Ctrl+C 停止服务器\n`);
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用`);
    console.log(`💡 请尝试:`);
    console.log(`   1. 更改端口: KWAIPILOT_AGENT_PORT=3001 node test-tcp-dev.js`);
    console.log(`   2. 或者停止占用端口的进程`);
  } else {
    console.error('❌ 服务器错误:', err.message);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到停止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ TCP 代理服务已停止');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ TCP 代理服务已停止');
    process.exit(0);
  });
});
