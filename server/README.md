# DeepSearch Server

DeepSearch 后端服务，基于 NestJS 框架构建，提供 AI 代理、代码搜索、索引管理等功能。

## 功能特性

- **AI 对话服务** - 支持多会话管理，智能问答
- **代码搜索** - 基于本地索引的代码搜索功能
- **索引管理** - 自动构建和管理代码仓库索引
- **项目信息** - 自动获取 Git 仓库信息
- **配置管理** - 灵活的环境变量配置
- **健康检查** - 服务状态监控

## 快速开始

### 环境要求

- Node.js >= 16
- pnpm (推荐) 或 npm
- Git 仓库环境

### 安装依赖

```bash
pnpm install
```

### 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置相关参数：

```env
# 服务端口
PORT=3001

# 二进制服务配置
BINARY_PATH=../local-agent/kwaipilot-binary
BINARY_CWD=../local-agent

# 代理配置
REPO_PATH=./
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=
AGENT_PREFERENCE=intelligent
USER_NAME=deepsearch-user
```

### 启动服务

开发模式：
```bash
pnpm dev
```

生产模式：
```bash
pnpm build
pnpm start
```

## API 接口

### 基础接口

- `GET /` - 服务状态
- `GET /health` - 健康检查

### 对话接口

- `POST /ask` - 简单问答
- `POST /sessions` - 创建会话
- `GET /sessions` - 获取会话列表
- `GET /sessions/:id` - 获取指定会话
- `POST /sessions/:id/messages` - 发送消息
- `POST /sessions/:id/delete` - 删除会话

### 搜索接口

- `POST /search` - 代码搜索

### 索引管理

- `GET /index/status` - 获取索引状态
- `POST /index/build` - 构建索引
- `POST /index/clear` - 清除索引

### 配置接口

- `GET /config` - 获取代理配置
- `GET /agent/status` - 获取代理状态

## 架构设计

### 核心模块

- **ConfigService** - 配置管理服务
- **ProjectService** - 项目信息管理
- **LocalService** - 本地二进制服务通信
- **AgentService** - AI 代理服务
- **ComposerService** - 对话管理服务

### 消息协议

服务采用基于 JSON 的消息协议与本地二进制服务通信，支持：

- 请求/响应模式
- 事件监听
- 错误处理
- 超时控制

## 开发指南

### 项目结构

```
src/
├── common/          # 公共模块
│   └── filters/     # 异常过滤器
├── config/          # 配置服务
├── core/            # 核心业务模块
│   ├── agent.ts     # AI 代理服务
│   ├── composer.ts  # 对话服务
│   ├── local-service.ts  # 本地服务通信
│   └── project.service.ts # 项目信息服务
├── app.controller.ts # 主控制器
├── app.module.ts    # 应用模块
└── main.ts          # 应用入口
```

### 添加新功能

1. 在 `core/` 目录下创建新的服务
2. 在 `app.module.ts` 中注册服务
3. 在 `app.controller.ts` 中添加 API 接口
4. 更新相关类型定义

## 故障排除

### 常见问题

1. **二进制服务启动失败**
   - 检查 `BINARY_PATH` 和 `BINARY_CWD` 配置
   - 确保二进制文件有执行权限

2. **索引构建失败**
   - 检查仓库路径配置
   - 确保是有效的 Git 仓库

3. **搜索功能异常**
   - 确保索引已构建完成
   - 检查本地服务连接状态

### 日志查看

服务日志会输出到控制台，包含：
- 服务启动信息
- 错误详情
- 请求处理状态

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

MIT License
