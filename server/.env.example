# DeepSearch Server 环境变量配置示例

# 服务端口
PORT=3001

# 二进制服务配置
BINARY_PATH=../local-agent/kwaipilot-binary
BINARY_CWD=../local-agent

# 代理配置
REPO_PATH=./
ENABLE_REPO_INDEX=true
MAX_INDEX_SPACE=10
PROXY_URL=
AGENT_PREFERENCE=intelligent
USER_NAME=deepsearch-user

# 日志级别
LOG_LEVEL=info

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# 健康检查配置
HEALTH_CHECK_INTERVAL=600000
HEALTH_CHECK_TIMEOUT=180000

# 重启配置
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=5000
