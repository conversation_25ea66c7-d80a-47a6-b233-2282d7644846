#!/bin/bash

# DeepSearch TCP 开发模式启动脚本
# 
# 这个脚本会同时启动 TCP 代理服务和 Web 服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 清理函数
cleanup() {
    print_info "正在停止服务..."
    
    # 停止后台进程
    if [ ! -z "$TCP_PID" ]; then
        kill $TCP_PID 2>/dev/null || true
        print_info "TCP 代理服务已停止"
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null || true
        print_info "Web 服务器已停止"
    fi
    
    print_success "所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查必要的文件
if [ ! -f "test-tcp-dev.js" ]; then
    print_error "找不到 test-tcp-dev.js 文件"
    print_info "请确保在 server 目录下运行此脚本"
    exit 1
fi

if [ ! -f "package.json" ]; then
    print_error "找不到 package.json 文件"
    print_info "请确保在 server 目录下运行此脚本"
    exit 1
fi

# 配置
TCP_PORT=${KWAIPILOT_AGENT_PORT:-3000}
WEB_PORT=${PORT:-3001}

print_info "DeepSearch TCP 开发模式启动器"
print_info "================================"
print_info "TCP 代理端口: $TCP_PORT"
print_info "Web 服务端口: $WEB_PORT"
echo

# 检查端口占用
if check_port $TCP_PORT; then
    print_warning "端口 $TCP_PORT 已被占用"
    print_info "如果是之前的测试服务，请先停止它"
    read -p "是否继续？(y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if check_port $WEB_PORT; then
    print_warning "端口 $WEB_PORT 已被占用"
    print_info "如果是之前的 Web 服务，请先停止它"
    read -p "是否继续？(y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动 TCP 代理服务
print_info "启动 TCP 代理服务..."
KWAIPILOT_AGENT_PORT=$TCP_PORT node test-tcp-dev.js &
TCP_PID=$!

# 等待 TCP 服务启动
sleep 2

# 检查 TCP 服务是否启动成功
if ! kill -0 $TCP_PID 2>/dev/null; then
    print_error "TCP 代理服务启动失败"
    exit 1
fi

print_success "TCP 代理服务已启动 (PID: $TCP_PID)"

# 启动 Web 服务器
print_info "启动 Web 服务器..."
NODE_ENV=development PORT=$WEB_PORT npm run start:dev &
WEB_PID=$!

# 等待 Web 服务启动
print_info "等待 Web 服务器启动..."
sleep 5

# 检查 Web 服务是否启动成功
if ! kill -0 $WEB_PID 2>/dev/null; then
    print_error "Web 服务器启动失败"
    cleanup
    exit 1
fi

print_success "Web 服务器已启动 (PID: $WEB_PID)"
echo

print_success "🎉 所有服务已启动！"
print_info "📡 TCP 代理服务: http://localhost:$TCP_PORT"
print_info "🌐 Web 界面: http://localhost:$WEB_PORT"
echo

print_info "💡 使用说明:"
print_info "   1. 访问 http://localhost:$WEB_PORT 打开 Web 界面"
print_info "   2. 创建新会话并发送消息测试 TCP 通信"
print_info "   3. 查看终端日志了解通信详情"
print_info "   4. 按 Ctrl+C 停止所有服务"
echo

print_warning "🔧 开发模式已启用，所有 AI 请求将通过 TCP 代理处理"

# 等待用户中断
wait
