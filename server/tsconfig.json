{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "dist", "baseUrl": ".", "incremental": true, "strict": true, "skipLibCheck": true, "paths": {"@deepsearch/shared": ["../packages/shared/dist"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../packages/shared"}]}