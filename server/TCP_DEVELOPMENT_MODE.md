# TCP Development Mode

## 概述

在开发环境下，DeepSearch Web 项目支持通过 TCP 连接到本地代理服务，而不是启动二进制服务。这种模式参考了 VSCode 插件的实现，使开发和调试更加便捷。

## 工作原理

### 生产模式
- 启动二进制服务进程
- 通过 stdin/stdout 进行通信
- 使用 JSON 消息格式

### 开发模式
- 连接到 TCP 服务器（默认端口 3000）
- 通过 TCP Socket 进行通信
- 使用相同的 JSON 消息格式
- 支持自动重连机制

## 配置

### 环境变量

```bash
# 启用开发模式
NODE_ENV=development

# TCP 代理服务配置
KWAIPILOT_AGENT_PORT=3000        # TCP 端口，默认 3000
KWAIPILOT_AGENT_HOST=127.0.0.1   # TCP 主机，默认 127.0.0.1
```

### 配置文件

也可以通过 ConfigService 进行配置：

```typescript
// 在 config.service.ts 中
this.config.KWAIPILOT_AGENT_PORT = 3000;
this.config.KWAIPILOT_AGENT_HOST = '127.0.0.1';
```

## 使用方法

### 快速开始

我们提供了一个测试脚本来快速验证 TCP 开发模式：

```bash
# 1. 启动 TCP 代理测试服务器
cd server
node test-tcp-dev.js

# 2. 在另一个终端启动 Web 服务器
NODE_ENV=development npm run start:dev

# 3. 访问 http://localhost:3001 测试
```

### 详细步骤

#### 1. 启动 TCP 代理服务

**选项 A: 使用测试脚本（推荐）**

```bash
cd server
node test-tcp-dev.js
```

这会启动一个模拟的 TCP 代理服务，支持基本的消息处理和响应。

**选项 B: 使用真实的代理服务**

如果你有真实的二进制代理服务，可以启动它并确保它监听在指定端口。

#### 2. 启动 Web 服务器

```bash
cd server
NODE_ENV=development npm run start:dev
```

#### 3. 查看连接状态

服务器启动时会显示连接状态：

```
[LocalService] 开发环境：使用TCP连接到本地代理服务
[TcpMessengerService] TCP连接成功: 127.0.0.1:3000
```

#### 4. 测试功能

访问 http://localhost:3001，创建会话并发送消息测试 TCP 通信。

## 消息格式

TCP 通信使用与二进制服务相同的消息格式：

```typescript
interface Message {
  messageType: string;
  messageId: string;
  data: any;
  common?: {
    version: string;
    platform: string;
    cwd: string;
    device: string;
    repo: {
      git_url: string;
      dir_path: string;
      commit: string;
    };
  };
}
```

## 重连机制

TcpMessengerService 包含自动重连功能：

- 最大重连次数：5 次
- 重连延迟：递增延迟（1秒 × 重连次数）
- 连接失败时会将消息加入待发送队列
- 重连成功后自动发送队列中的消息

## 调试

### 日志级别

设置日志级别查看详细信息：

```bash
LOG_LEVEL=debug NODE_ENV=development npm run start:dev
```

### 常见日志消息

```
📨 收到TCP消息: assistant/agent/local (ID: uuid)
📤 发送TCP消息: assistant/agent/local (ID: uuid)
📝 注册TCP处理器: assistant/agent/message
```

## 故障排除

### 连接失败

1. 检查 TCP 代理服务是否运行
2. 确认端口配置正确
3. 检查防火墙设置

### 消息超时

- 默认超时时间：30 秒
- 检查 TCP 代理服务响应时间
- 确认消息格式正确

### 重连问题

- 检查网络连接
- 确认 TCP 代理服务稳定性
- 查看重连日志

## 与 VSCode 插件的对比

| 特性 | VSCode 插件 | Web 项目 |
|------|-------------|----------|
| 开发模式检测 | `process.env.NODE_ENV !== "development"` | `process.env.NODE_ENV === 'development'` |
| TCP 端口 | `KWAIPILOT_AGENT_PORT` 或 3000 | 同左 |
| 重连机制 | ✅ | ✅ |
| 消息格式 | JSON + `\r\n` | JSON + `\n` |
| 错误处理 | ✅ | ✅ |
| 待发送队列 | ✅ | ✅ |

## 示例 TCP 代理服务

可以使用 Node.js 创建一个简单的 TCP 代理服务进行测试：

```typescript
import * as net from 'net';

const server = net.createServer((socket) => {
  console.log('客户端连接');
  
  socket.on('data', (data) => {
    const message = JSON.parse(data.toString());
    console.log('收到消息:', message);
    
    // 模拟响应
    const response = {
      messageType: message.messageType,
      messageId: message.messageId,
      data: { status: 'ok', result: 'mock response' }
    };
    
    socket.write(JSON.stringify(response) + '\n');
  });
});

server.listen(3000, () => {
  console.log('TCP 代理服务启动在端口 3000');
});
```
