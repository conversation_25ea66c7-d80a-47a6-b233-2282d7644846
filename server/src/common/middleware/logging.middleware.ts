import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl, body, params, query } = req;
    const userAgent = req.get('User-Agent') || '';
    const startTime = Date.now();

    // 记录请求信息
    this.logger.log(
      `${method} ${originalUrl} - ${userAgent}`,
    );

    // 记录请求参数（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      if (Object.keys(params).length > 0) {
        this.logger.debug(`Params: ${JSON.stringify(params)}`);
      }
      if (Object.keys(query).length > 0) {
        this.logger.debug(`Query: ${JSON.stringify(query)}`);
      }
      if (body && Object.keys(body).length > 0) {
        // 避免记录敏感信息
        const safeBody = { ...body };
        if (safeBody.password) safeBody.password = '[HIDDEN]';
        if (safeBody.token) safeBody.token = '[HIDDEN]';
        this.logger.debug(`Body: ${JSON.stringify(safeBody)}`);
      }
    }

    // 监听响应完成
    res.on('finish', () => {
      const { statusCode } = res;
      const contentLength = res.get('content-length');
      const duration = Date.now() - startTime;

      const logLevel = statusCode >= 400 ? 'error' : 'log';
      this.logger[logLevel](
        `${method} ${originalUrl} ${statusCode} ${contentLength || 0}b - ${duration}ms`,
      );
    });

    next();
  }
}
