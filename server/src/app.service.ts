import { Injectable, Logger } from '@nestjs/common';
import { ComposerService, AgentService } from './core';
import { AgentConfig, IndexState } from './core/agent';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(
    private readonly composerService: ComposerService,
    private readonly agentService: AgentService,
  ) {}

  /**
   * 回答问题
   * @param question 用户问题
   * @returns 答案文本
   */
  async answerQuestion(question: string): Promise<string> {
    try {
      this.logger.log(`处理问题: ${question.slice(0, 50)}${question.length > 50 ? '...' : ''}`);
      
      // 使用 ComposerService 处理问题
      return await this.composerService.ask(question);
    } catch (error) {
       const errorMessage = (error instanceof Error) ? error.message : String(error);
      this.logger.error(`回答问题失败: ${errorMessage}`);
      throw new Error(`处理问题时发生错误: ${errorMessage}`);
    }
  }

  /**
   * 搜索代码库
   * @param query 搜索查询
   * @param limit 结果限制数量
   */
  async searchCodebase(query: string, limit = 20) {
    try {
      this.logger.log(`搜索代码: ${query}`);
      
      // 使用 AgentService 搜索代码库
      const result = await this.agentService.search(query, limit);
      return result;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      this.logger.error(`搜索代码失败: ${errorMessage}`);
      throw new Error(`搜索代码时发生错误: ${errorMessage}`);
    }
  }

  /**
   * 获取代理服务状态
   */
  getAgentStatus(): { indexState: IndexState; config: AgentConfig; isRunning: boolean; isReady: boolean } {
    const config = this.agentService.getConfig();
    const indexState = this.agentService.getIndexState();
    
    return {
      indexState,
      config,
      isRunning: indexState.indexing,
      isReady: indexState.indexed && !indexState.indexing,
    };
  }

  /**
   * 开始索引代码库
   */
  startIndexing(): IndexState {
    this.logger.log('开始索引代码库');
    return this.agentService.startRepoIndex();
  }

  /**
   * 清除代码库索引
   */
  clearIndex(): IndexState {
    this.logger.log('清除代码库索引');
    return this.agentService.clearIndex();
  }

  /**
   * 获取代理配置
   */
  getAgentConfig(): AgentConfig {
    return this.agentService.getConfig();
  }
}
