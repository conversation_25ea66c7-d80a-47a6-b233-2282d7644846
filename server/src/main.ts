import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from './config/config.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 启用 CORS
  const corsConfig = configService.getCorsConfig();
  app.enableCors({
    origin: corsConfig.origin,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: corsConfig.credentials,
  });

  const port = configService.getPort();
  await app.listen(port);
  console.log(`Deepsearch server running on http://localhost:${port}`);
}
bootstrap();
