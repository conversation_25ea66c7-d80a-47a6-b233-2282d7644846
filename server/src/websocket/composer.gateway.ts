import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
} from "@nestjs/websockets";
import { Logger } from "@nestjs/common";
import { Server, Socket } from "socket.io";
import { EventBusService } from "../core/event-bus";

/**
 * Composer WebSocket 网关 - 处理实时消息推送
 * 参考 VSCode 插件中的 bridge.postOneWayMessage 实现
 */
@WebSocketGateway({
  cors: {
    origin: "*",
    credentials: false,
  },
  namespace: "/composer",
})
export class ComposerGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(ComposerGateway.name);
  private sessionConnections = new Map<string, Set<string>>(); // sessionId -> Set<socketId>

  constructor(private readonly eventBus: EventBusService) {
    // 设置事件监听器
    this.setupComposerEventListeners();
  }

  afterInit(server: Server) {
    this.server = server;
    this.logger.log("WebSocket 服务器初始化完成");
  }

  handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`客户端断开连接: ${client.id}`);

    // 清理连接映射
    for (const [sessionId, socketIds] of this.sessionConnections.entries()) {
      socketIds.delete(client.id);
      if (socketIds.size === 0) {
        this.sessionConnections.delete(sessionId);
      }
    }
  }

  /**
   * 客户端订阅会话
   */
  @SubscribeMessage("subscribeSession")
  handleSubscribeSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket
  ) {
    const { sessionId } = data;
    this.logger.log(`客户端 ${client.id} 订阅会话: ${sessionId}`);

    if (!this.sessionConnections.has(sessionId)) {
      this.sessionConnections.set(sessionId, new Set());
    }
    this.sessionConnections.get(sessionId)!.add(client.id);

    client.emit("subscribed", { sessionId });
  }

  /**
   * 客户端取消订阅会话
   */
  @SubscribeMessage("unsubscribeSession")
  handleUnsubscribeSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: Socket
  ) {
    const { sessionId } = data;
    this.logger.log(`客户端 ${client.id} 取消订阅会话: ${sessionId}`);

    const socketIds = this.sessionConnections.get(sessionId);
    if (socketIds) {
      socketIds.delete(client.id);
      if (socketIds.size === 0) {
        this.sessionConnections.delete(sessionId);
      }
    }

    client.emit("unsubscribed", { sessionId });
  }

  /**
   * 设置事件总线监听器
   */
  private setupComposerEventListeners() {
    // 监听状态更新事件，参考 VSCode 插件的 COMPOSER_STATE_UPDATE
    this.eventBus.on("composerStateUpdate", (event: any) => {
      this.broadcastToSession(event.sessionId, "composerStateUpdate", event);
    });

    // 监听部分消息事件，参考 VSCode 插件的 COMPOSER_PARTIAL_MESSAGE
    this.eventBus.on("composerPartialMessage", (event: any) => {
      this.broadcastToSession(event.sessionId, "composerPartialMessage", event);
    });
  }

  /**
   * 向特定会话的所有客户端广播消息
   */
  private broadcastToSession(sessionId: string, event: string, data: any) {
    const socketIds = this.sessionConnections.get(sessionId);
    if (!socketIds || socketIds.size === 0) {
      this.logger.debug(`会话 ${sessionId} 没有活跃连接`);
      return;
    }

    this.logger.debug(
      `向会话 ${sessionId} 的 ${socketIds.size} 个客户端广播事件: ${event}`
    );

    // 使用 Socket.IO 的正确方式：直接向 socket ID 发送消息
    for (const socketId of socketIds) {
      this.server.to(socketId).emit(event, data);
    }
  }

  /**
   * 向所有客户端广播消息
   */
  broadcastToAll(event: string, data: any) {
    this.logger.debug(`向所有客户端广播事件: ${event}`);
    this.server.emit(event, data);
  }
}
