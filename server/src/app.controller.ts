import { Controller, Get, Post, Body, Param, HttpStatus, HttpException } from '@nestjs/common';
import { AppService } from './app.service';
import { ComposerService, AgentService, LocalService } from './core';
import {
  ApiRequests,
  ApiResponses,
} from '@deepsearch/shared';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly composerService: ComposerService,
    private readonly agentService: AgentService,
    private readonly localService: LocalService,
  ) { }

  @Get()
  getHello(): string {
    return 'Deepsearch API is running';
  }

  // 简单问答接口
  @Post('ask')
  async ask(@Body() req: ApiRequests.Question): Promise<ApiResponses.Answer> {
    if (!req.question || typeof req.question !== 'string') {
      throw new HttpException('无效的请求格式，缺少问题内容', HttpStatus.BAD_REQUEST);
    }

    try {
      const answer = await this.appService.answerQuestion(req.question);
      return { answer };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`处理问题失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 创建新会话
  @Post('sessions')
  async createSession(@Body() req: ApiRequests.CreateSession = {}): Promise<ApiResponses.SessionCreated> {
    try {
      console.log('Creating new session with request:', req);

      const session = this.composerService.createSession({
        model: req.model,
        systemPrompt: req.systemPrompt,
      });

      if (req.name) {
        // 如果提供了名称，更新会话名称
        session.name = req.name;
      }

      const result = {
        id: session.id,
        name: session.name || `对话 ${new Date().toLocaleDateString()}`,
        createdAt: session.createdAt,
      };

      // 记录创建的会话信息
      console.log('✅ Session created successfully:', result);

      return result;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      console.error('❌ Session creation failed:', errorMessage);
      throw new HttpException(`创建会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取所有会话
  @Get('sessions')
  async getSessions() {
    try {
      const sessions = this.composerService.getAllSessions();
      return sessions.map(session => ({
        id: session.id,
        name: session.name || `对话 ${new Date(session.createdAt).toLocaleDateString()}`,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        messageCount: session.messages.filter(msg => msg.role !== 'system').length,
      }));
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取指定会话
  @Get('sessions/:id')
  async getSession(@Param('id') id: string) {
    try {
      // 验证会话ID
      if (!id || id.trim() === '') {
        throw new HttpException('会话ID不能为空', HttpStatus.BAD_REQUEST);
      }

      const session = this.composerService.getSession(id);
      if (!session) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      return {
        id: session.id,
        name: session.name || `对话 ${new Date(session.createdAt).toLocaleDateString()}`,
        messages: session.messages.filter(msg => msg.role !== 'system'),
        config: session.config,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 智能消息发送 - 如果没有会话ID则自动创建
  @Post('chat')
  async smartChat(@Body() req: { content: string; sessionId?: string }) {
    try {
      console.log('Smart chat request:', req);

      let sessionId = req.sessionId;

      // 如果没有提供会话ID或会话不存在，创建新会话
      if (!sessionId || !this.composerService.getSession(sessionId)) {
        console.log('Creating new session for smart chat');
        const session = this.composerService.createSession();
        sessionId = session.id;
        console.log('✅ Auto-created session:', sessionId);
      }

      const reply = await this.composerService.sendMessage(sessionId, req.content);

      return {
        id: reply.id || '',
        content: reply.content,
        role: reply.role,
        createdAt: reply.createdAt || Date.now(),
        sessionId, // 返回使用的会话ID
        autoCreated: req.sessionId !== sessionId, // 标记是否自动创建
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      console.error('❌ Smart chat failed:', errorMessage);
      throw new HttpException(`发送消息失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 发送消息到指定会话
  @Post('sessions/:id/messages')
  async sendMessage(
    @Param('id') id: string,
    @Body() req: ApiRequests.SendMessage
  ): Promise<ApiResponses.MessageResponse> {
    try {
      // 验证会话ID
      if (!id || id.trim() === '') {
        throw new HttpException('会话ID不能为空', HttpStatus.BAD_REQUEST);
      }

      if (!req.content || typeof req.content !== 'string') {
        throw new HttpException('无效的请求格式，缺少消息内容', HttpStatus.BAD_REQUEST);
      }

      // 检查会话是否存在
      const session = this.composerService.getSession(id);
      if (!session) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      const reply = await this.composerService.sendMessage(id, req.content);

      return {
        id: reply.id || '',
        content: reply.content,
        role: reply.role,
        createdAt: reply.createdAt || Date.now(),
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`发送消息失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 删除会话
  @Post('sessions/:id/delete')
  async deleteSession(@Param('id') id: string) {
    try {
      // 验证会话ID
      if (!id || id.trim() === '') {
        throw new HttpException('会话ID不能为空', HttpStatus.BAD_REQUEST);
      }

      const success = this.composerService.deleteSession(id);
      if (!success) {
        throw new HttpException(`会话不存在: ${id}`, HttpStatus.NOT_FOUND);
      }

      return { success: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`删除会话失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 搜索接口
  @Post('search')
  async search(@Body() req: { query: string; limit?: number }) {
    try {
      if (!req.query || typeof req.query !== 'string') {
        throw new HttpException('无效的搜索查询', HttpStatus.BAD_REQUEST);
      }

      const result = await this.agentService.search(req.query, req.limit || 20);
      return result;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`搜索失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取索引状态
  @Get('index/status')
  async getIndexStatus() {
    try {
      const indexState = this.agentService.getIndexState();
      return indexState;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取索引状态失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 开始构建索引
  @Post('index/build')
  async buildIndex() {
    try {
      const result = this.agentService.startRepoIndex();
      return result;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`构建索引失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 清除索引
  @Post('index/clear')
  async clearIndex() {
    try {
      const result = this.agentService.clearIndex();
      return result;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`清除索引失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取代理配置
  @Get('config')
  async getConfig() {
    try {
      const config = this.agentService.getConfig();
      return config;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取配置失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 检查代理状态
  @Get('agent/status')
  async getAgentStatus() {
    try {
      const status = await this.agentService.checkAgentState();
      return status;
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      throw new HttpException(`获取代理状态失败: ${errorMessage}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 调试端点：获取所有会话的详细信息
  @Get('debug/sessions')
  async debugSessions() {
    const sessions = this.composerService.getAllSessions();
    return {
      total: sessions.length,
      sessions: sessions.map(session => ({
        id: session.id,
        name: session.name,
        messageCount: session.messages.length,
        createdAt: new Date(session.createdAt).toISOString(),
        updatedAt: new Date(session.updatedAt).toISOString(),
      })),
    };
  }

  // 服务状态检查端点
  @Get('debug/status')
  async debugStatus() {
    try {
      const serviceStats = this.composerService.getServiceStats();
      const localServiceStatus = await this.composerService.checkLocalServiceStatus();
      const agentStatus = await this.agentService.checkAgentState();

      return {
        timestamp: new Date().toISOString(),
        services: {
          composer: serviceStats,
          localService: localServiceStatus,
          agent: agentStatus,
        },
        health: {
          overall: localServiceStatus.isRunning ? 'healthy' : 'unhealthy',
          details: localServiceStatus.message,
        },
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      return {
        timestamp: new Date().toISOString(),
        error: errorMessage,
        health: {
          overall: 'error',
          details: `状态检查失败: ${errorMessage}`,
        },
      };
    }
  }

  // 重新注册事件处理器
  @Post('debug/reregister-handlers')
  async reregisterHandlers() {
    try {
      console.log('重新注册事件处理器');
      this.agentService.reregisterEventHandlers();

      return {
        success: true,
        message: '事件处理器重新注册完成',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      console.error('重新注册事件处理器失败:', errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // 测试本地服务消息处理
  @Post('test/local-message')
  async testLocalMessage(@Body() req: { messageType: string; data?: any }): Promise<any> {
    try {
      console.log('Testing local service message:', req);

      // 直接调用本地服务的请求方法
      const response = await this.localService.request(req.messageType as any, req.data);

      return {
        success: true,
        messageType: req.messageType,
        request: req.data,
        response,
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      console.error('Test local message error:', errorMessage);
      return {
        success: false,
        error: errorMessage,
        messageType: req.messageType,
      };
    }
  }

  // 测试会话创建和消息发送
  @Post('test/chat')
  async testChat(@Body() req: { message: string }) {
    try {
      // 创建新会话
      const session = this.composerService.createSession();
      console.log('Test session created:', session.id);

      // 发送消息
      const reply = await this.composerService.sendMessage(session.id, req.message || 'Hello');
      console.log('Test reply:', reply);

      return {
        sessionId: session.id,
        message: req.message || 'Hello',
        reply: reply.content,
        success: true,
      };
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : String(error);
      console.error('Test chat error:', errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // 健康检查接口
  @Get('health')
  async healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    };
  }
}
