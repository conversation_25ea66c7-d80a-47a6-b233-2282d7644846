import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '../config/config.service';
import { execSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

/**
 * 项目信息接口
 */
export interface ProjectInfo {
  name: string;
  gitRepo: string;
  gitRemote: string;
  currentBranchName: string;
  username: string;
  userEmail: string;
  repoPath: string;
  currentCommit: string;
}

/**
 * 项目服务 - 管理项目和Git信息
 */
@Injectable()
export class ProjectService {
  private readonly logger = new Logger(ProjectService.name);
  private projectInfo: ProjectInfo | null = null;

  constructor(private readonly configService: ConfigService) {
    this.loadProjectInfo();
  }

  /**
   * 加载项目信息
   */
  private loadProjectInfo() {
    try {
      const repoPath = this.configService.getRepoPath();
      
      if (!this.isGitRepository(repoPath)) {
        this.logger.warn(`目录不是Git仓库: ${repoPath}`);
        return;
      }

      this.projectInfo = {
        name: this.getProjectName(repoPath),
        gitRepo: this.getGitRepo(repoPath),
        gitRemote: this.getRemoteOriginUrlPrivate(repoPath),
        currentBranchName: this.getCurrentBranchPrivate(repoPath),
        username: this.getGitUsername(repoPath),
        userEmail: this.getGitUserEmail(repoPath),
        repoPath,
        currentCommit: this.getCurrentCommitPrivate(repoPath),
      };

      this.logger.log(`项目信息加载成功: ${this.projectInfo.name}`);
    } catch (error) {
      this.logger.error('加载项目信息失败', error);
    }
  }

  /**
   * 检查是否为Git仓库
   */
  private isGitRepository(repoPath: string): boolean {
    try {
      const gitDir = path.join(repoPath, '.git');
      return fs.existsSync(gitDir);
    } catch {
      return false;
    }
  }

  /**
   * 获取项目名称
   */
  private getProjectName(repoPath: string): string {
    try {
      return path.basename(repoPath);
    } catch {
      return 'unknown-project';
    }
  }

  /**
   * 获取Git仓库名称
   */
  private getGitRepo(repoPath: string): string {
    try {
      const remoteUrl = this.getRemoteOriginUrlPrivate(repoPath);
      if (!remoteUrl) return '';
      
      // 从远程URL中提取仓库名
      const match = remoteUrl.match(/\/([^\/]+?)(?:\.git)?$/);
      return match ? match[1] : '';
    } catch {
      return '';
    }
  }

  /**
   * 获取远程仓库URL (私有方法)
   */
  private getRemoteOriginUrlPrivate(repoPath: string): string {
    try {
      return execSync('git config --get remote.origin.url', {
        cwd: repoPath,
        encoding: 'utf8',
      }).trim();
    } catch {
      return '';
    }
  }

  /**
   * 获取当前分支名 (私有方法)
   */
  private getCurrentBranchPrivate(repoPath: string): string {
    try {
      return execSync('git rev-parse --abbrev-ref HEAD', {
        cwd: repoPath,
        encoding: 'utf8',
      }).trim();
    } catch {
      return '';
    }
  }

  /**
   * 获取Git用户名
   */
  private getGitUsername(repoPath: string): string {
    try {
      return execSync('git config --get user.name', {
        cwd: repoPath,
        encoding: 'utf8',
      }).trim();
    } catch {
      return '';
    }
  }

  /**
   * 获取Git用户邮箱
   */
  private getGitUserEmail(repoPath: string): string {
    try {
      return execSync('git config --get user.email', {
        cwd: repoPath,
        encoding: 'utf8',
      }).trim();
    } catch {
      return '';
    }
  }

  /**
   * 获取当前提交ID (私有方法)
   */
  private getCurrentCommitPrivate(repoPath: string): string {
    try {
      return execSync('git rev-parse HEAD', {
        cwd: repoPath,
        encoding: 'utf8',
      }).trim();
    } catch {
      return '';
    }
  }

  /**
   * 获取项目信息
   */
  getProjectInfo(): ProjectInfo | null {
    return this.projectInfo;
  }

  /**
   * 获取仓库路径
   */
  getRepoPath(): string {
    return this.projectInfo?.repoPath || this.configService.getRepoPath();
  }

  /**
   * 获取远程仓库URL
   */
  getRemoteOriginUrl(): string {
    return this.projectInfo?.gitRemote || '';
  }

  /**
   * 获取当前提交ID
   */
  getCurrentCommit(): string {
    return this.projectInfo?.currentCommit || '';
  }

  /**
   * 获取当前分支名
   */
  getCurrentBranch(): string {
    return this.projectInfo?.currentBranchName || '';
  }

  /**
   * 刷新项目信息
   */
  refresh(): void {
    this.loadProjectInfo();
  }

  /**
   * 获取相对路径
   */
  getRelativePath(absolutePath: string): string {
    const repoPath = this.getRepoPath();
    if (!repoPath || !absolutePath.startsWith(repoPath)) {
      return absolutePath;
    }
    return path.relative(repoPath, absolutePath);
  }

  /**
   * 获取绝对路径
   */
  getAbsolutePath(relativePath: string): string {
    const repoPath = this.getRepoPath();
    return path.resolve(repoPath, relativePath);
  }
}
