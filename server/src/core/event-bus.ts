import { Injectable } from '@nestjs/common';
import { EventEmitter } from 'events';

/**
 * 全局事件总线 - 用于在不同模块间传递事件
 */
@Injectable()
export class EventBusService extends EventEmitter {
  constructor() {
    super();
    this.setMaxListeners(100); // 增加最大监听器数量
  }

  /**
   * 发送状态更新事件
   */
  emitComposerStateUpdate(sessionId: string, data: any) {
    this.emit('composerStateUpdate', {
      type: 'COMPOSER_STATE_UPDATE',
      sessionId,
      data,
    });
  }

  /**
   * 发送部分消息事件
   */
  emitComposerPartialMessage(sessionId: string, partialMessage: any) {
    this.emit('composerPartialMessage', {
      type: 'COMPOSER_PARTIAL_MESSAGE',
      sessionId,
      data: {
        partialMessage,
      },
    });
  }
}
