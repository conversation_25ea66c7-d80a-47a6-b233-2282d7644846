import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as sqlite3 from 'sqlite3';
import * as path from 'path';
import * as fs from 'fs';
import { Session, Message } from '@deepsearch/shared';

/**
 * 数据库服务 - 使用 SQLite 进行会话持久化
 */
@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DatabaseService.name);
  private db: sqlite3.Database | null = null;
  private readonly dbPath: string;

  constructor() {
    // 数据库文件路径
    const storageDir = path.join(process.cwd(), '.deepsearch');
    this.dbPath = path.join(storageDir, 'sessions.db');
    
    // 确保存储目录存在
    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, { recursive: true });
    }
  }

  async onModuleInit() {
    await this.initDatabase();
  }

  async onModuleDestroy() {
    if (this.db) {
      await this.closeDatabase();
    }
  }

  /**
   * 初始化数据库连接和表结构
   */
  private async initDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          this.logger.error('数据库连接失败:', err);
          reject(err);
          return;
        }

        this.logger.log(`数据库连接成功: ${this.dbPath}`);
        
        // 创建表结构
        this.createTables()
          .then(() => resolve())
          .catch(reject);
      });
    });
  }

  /**
   * 创建数据库表
   */
  private async createTables(): Promise<void> {
    const createSessionsTable = `
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        name TEXT,
        config TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    `;

    const createMessagesTable = `
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        role TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
      )
    `;

    const createIndexes = [
      'CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages (session_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_updated_at ON sessions (updated_at)',
    ];

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      this.db.serialize(() => {
        this.db!.run(createSessionsTable, (err) => {
          if (err) {
            this.logger.error('创建 sessions 表失败:', err);
            reject(err);
            return;
          }
        });

        this.db!.run(createMessagesTable, (err) => {
          if (err) {
            this.logger.error('创建 messages 表失败:', err);
            reject(err);
            return;
          }
        });

        // 创建索引
        createIndexes.forEach(indexSql => {
          this.db!.run(indexSql, (err) => {
            if (err) {
              this.logger.error('创建索引失败:', err);
            }
          });
        });

        this.logger.log('数据库表创建完成');
        resolve();
      });
    });
  }

  /**
   * 关闭数据库连接
   */
  private async closeDatabase(): Promise<void> {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            this.logger.error('关闭数据库失败:', err);
          } else {
            this.logger.log('数据库连接已关闭');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 保存会话
   */
  async saveSession(session: Session): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        INSERT OR REPLACE INTO sessions (id, name, config, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `;

      this.db.run(
        sql,
        [
          session.id,
          session.name || null,
          JSON.stringify(session.config),
          session.createdAt,
          session.updatedAt,
        ],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * 保存消息
   */
  async saveMessage(sessionId: string, message: Message): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        INSERT OR REPLACE INTO messages (id, session_id, role, content, created_at)
        VALUES (?, ?, ?, ?, ?)
      `;

      this.db.run(
        sql,
        [message.id, sessionId, message.role, message.content, message.createdAt],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * 获取所有会话
   */
  async getAllSessions(): Promise<Session[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        SELECT s.*, 
               COUNT(m.id) as message_count
        FROM sessions s
        LEFT JOIN messages m ON s.id = m.session_id
        GROUP BY s.id
        ORDER BY s.updated_at DESC
      `;

      this.db.all(sql, [], async (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const sessions: Session[] = [];
          
          for (const row of rows) {
            const messages = await this.getSessionMessages(row.id);
            sessions.push({
              id: row.id,
              name: row.name,
              config: JSON.parse(row.config),
              messages,
              createdAt: row.created_at,
              updatedAt: row.updated_at,
            });
          }

          resolve(sessions);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 获取单个会话
   */
  async getSession(id: string): Promise<Session | undefined> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = 'SELECT * FROM sessions WHERE id = ?';

      this.db.get(sql, [id], async (err, row: any) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(undefined);
          return;
        }

        try {
          const messages = await this.getSessionMessages(id);
          resolve({
            id: row.id,
            name: row.name,
            config: JSON.parse(row.config),
            messages,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
          });
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 获取会话的所有消息
   */
  async getSessionMessages(sessionId: string): Promise<Message[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = `
        SELECT * FROM messages 
        WHERE session_id = ? 
        ORDER BY created_at ASC
      `;

      this.db.all(sql, [sessionId], (err, rows: any[]) => {
        if (err) {
          reject(err);
          return;
        }

        const messages: Message[] = rows.map(row => ({
          id: row.id,
          role: row.role as 'user' | 'assistant' | 'system',
          content: row.content,
          createdAt: row.created_at,
        }));

        resolve(messages);
      });
    });
  }

  /**
   * 删除会话
   */
  async deleteSession(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      // 先删除消息，再删除会话
      this.db.serialize(() => {
        this.db!.run('DELETE FROM messages WHERE session_id = ?', [id], (err) => {
          if (err) {
            reject(err);
            return;
          }
        });

        this.db!.run('DELETE FROM sessions WHERE id = ?', [id], function (err) {
          if (err) {
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        });
      });
    });
  }

  /**
   * 更新会话的更新时间
   */
  async updateSessionTimestamp(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const sql = 'UPDATE sessions SET updated_at = ? WHERE id = ?';
      
      this.db.run(sql, [Date.now(), id], function (err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
}
