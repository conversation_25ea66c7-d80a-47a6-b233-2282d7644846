#!/usr/bin/env node

/**
 * AI服务连接测试脚本
 * 测试本地二进制服务的连接和AI功能
 */

const http = require('http');

const API_URL = 'http://localhost:3001';

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求封装
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testHealthCheck() {
  log('\n1. 测试健康检查...', 'blue');
  try {
    const response = await makeRequest('/health');
    if (response.status === 200) {
      log('✓ 服务健康检查通过', 'green');
      return true;
    } else {
      log(`✗ 健康检查失败: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`✗ 健康检查失败: ${error.message}`, 'red');
    return false;
  }
}

async function testServiceStatus() {
  log('\n2. 测试服务状态...', 'blue');
  try {
    const response = await makeRequest('/debug/status');
    if (response.status === 200) {
      const status = response.data;
      log(`✓ 服务状态检查完成`, 'green');
      log(`  - 整体健康状态: ${status.health.overall}`, 
          status.health.overall === 'healthy' ? 'green' : 'red');
      log(`  - 本地服务运行: ${status.services.localService.isRunning}`, 
          status.services.localService.isRunning ? 'green' : 'red');
      log(`  - 活跃会话数: ${status.services.composer.activeSessions}`);
      log(`  - 总消息数: ${status.services.composer.totalMessages}`);
      return status.health.overall === 'healthy';
    } else {
      log(`✗ 服务状态检查失败: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`✗ 服务状态检查失败: ${error.message}`, 'red');
    return false;
  }
}

async function testSessionCreation() {
  log('\n3. 测试会话创建...', 'blue');
  try {
    const response = await makeRequest('/sessions', 'POST', {
      name: '测试会话 ' + new Date().toLocaleString()
    });
    if (response.status === 201 || response.status === 200) {
      log('✓ 会话创建成功', 'green');
      log(`  - 会话ID: ${response.data.id}`);
      return response.data.id;
    } else {
      log(`✗ 会话创建失败: ${response.status}`, 'red');
      return null;
    }
  } catch (error) {
    log(`✗ 会话创建失败: ${error.message}`, 'red');
    return null;
  }
}

async function testAIChat() {
  log('\n4. 测试AI对话...', 'blue');
  try {
    const response = await makeRequest('/chat', 'POST', {
      content: '你好，请简单介绍一下你自己。'
    });
    if (response.status === 200) {
      log('✓ AI对话测试成功', 'green');
      log(`  - 回复内容: ${response.data.content.slice(0, 100)}...`);
      log(`  - 会话ID: ${response.data.sessionId}`);
      log(`  - 自动创建: ${response.data.autoCreated}`);
      return true;
    } else {
      log(`✗ AI对话测试失败: ${response.status}`, 'red');
      log(`  - 错误信息: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    log(`✗ AI对话测试失败: ${error.message}`, 'red');
    return false;
  }
}

async function testSimpleAsk() {
  log('\n5. 测试简单问答...', 'blue');
  try {
    const response = await makeRequest('/ask', 'POST', {
      question: '什么是人工智能？'
    });
    if (response.status === 200) {
      log('✓ 简单问答测试成功', 'green');
      log(`  - 回答: ${response.data.answer.slice(0, 100)}...`);
      return true;
    } else {
      log(`✗ 简单问答测试失败: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`✗ 简单问答测试失败: ${error.message}`, 'red');
    return false;
  }
}

async function main() {
  log('DeepSearch AI服务连接测试', 'blue');
  log('=' * 50, 'blue');

  const results = [];
  
  results.push(await testHealthCheck());
  results.push(await testServiceStatus());
  results.push(await testSessionCreation() !== null);
  results.push(await testAIChat());
  results.push(await testSimpleAsk());

  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;

  log('\n' + '=' * 50, 'blue');
  log(`测试完成: ${passedTests}/${totalTests} 通过`, 
      passedTests === totalTests ? 'green' : 'yellow');

  if (passedTests === totalTests) {
    log('✓ 所有测试通过，AI服务工作正常！', 'green');
    process.exit(0);
  } else {
    log('⚠ 部分测试失败，请检查服务配置', 'yellow');
    log('\n建议检查项:', 'yellow');
    log('1. 确保本地二进制服务正在运行', 'yellow');
    log('2. 检查网络连接和代理设置', 'yellow');
    log('3. 查看服务日志获取详细错误信息', 'yellow');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
