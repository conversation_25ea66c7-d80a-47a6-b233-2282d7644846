#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 环境检查脚本
 * 检查必要的环境配置和依赖
 */

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    log(`✓ ${description}: ${filePath}`, 'green');
  } else {
    log(`✗ ${description}: ${filePath} (不存在)`, 'red');
  }
  return exists;
}

function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  if (exists) {
    log(`✓ ${description}: ${dirPath}`, 'green');
  } else {
    log(`✗ ${description}: ${dirPath} (不存在或不是目录)`, 'red');
  }
  return exists;
}

function checkEnvVar(varName, description, required = true) {
  const value = process.env[varName];
  if (value) {
    log(`✓ ${description}: ${varName}=${value}`, 'green');
    return true;
  } else if (required) {
    log(`✗ ${description}: ${varName} (未设置)`, 'red');
    return false;
  } else {
    log(`⚠ ${description}: ${varName} (可选，未设置)`, 'yellow');
    return true;
  }
}

function main() {
  log('DeepSearch Server 环境检查', 'blue');
  log('=' * 50, 'blue');

  let allChecksPass = true;

  // 检查环境变量文件
  log('\n1. 检查配置文件:', 'yellow');
  const envExists = checkFile('.env', '环境变量文件');
  const envExampleExists = checkFile('.env.example', '环境变量示例文件');

  if (!envExists && envExampleExists) {
    log('  提示: 可以复制 .env.example 到 .env 并修改配置', 'yellow');
  }

  // 加载环境变量
  if (envExists) {
    require('dotenv').config();
  }

  // 检查关键环境变量
  log('\n2. 检查环境变量:', 'yellow');
  allChecksPass &= checkEnvVar('PORT', '服务端口', false);
  allChecksPass &= checkEnvVar('BINARY_PATH', '二进制服务路径');
  allChecksPass &= checkEnvVar('BINARY_CWD', '二进制服务工作目录');
  allChecksPass &= checkEnvVar('REPO_PATH', '仓库路径', false);

  // 检查二进制服务
  log('\n3. 检查二进制服务:', 'yellow');
  const binaryPath = process.env.BINARY_PATH || path.resolve(__dirname, '../../../local-agent/kwaipilot-binary');
  const binaryCwd = process.env.BINARY_CWD || path.resolve(__dirname, '../../../local-agent');
  
  allChecksPass &= checkFile(binaryPath, '二进制服务文件');
  allChecksPass &= checkDirectory(binaryCwd, '二进制服务目录');

  // 检查仓库
  log('\n4. 检查代码仓库:', 'yellow');
  const repoPath = process.env.REPO_PATH || process.cwd();
  allChecksPass &= checkDirectory(repoPath, '仓库目录');
  
  const gitDir = path.join(repoPath, '.git');
  const isGitRepo = checkDirectory(gitDir, 'Git 仓库');
  if (!isGitRepo) {
    log('  警告: 不是有效的 Git 仓库，某些功能可能无法正常工作', 'yellow');
  }

  // 检查依赖
  log('\n5. 检查项目依赖:', 'yellow');
  allChecksPass &= checkFile('package.json', 'package.json');
  allChecksPass &= checkDirectory('node_modules', 'node_modules');
  allChecksPass &= checkDirectory('../packages/shared', 'shared 包');

  // 检查构建文件
  log('\n6. 检查构建文件:', 'yellow');
  checkFile('tsconfig.json', 'TypeScript 配置');
  checkFile('nest-cli.json', 'NestJS 配置');

  // 总结
  log('\n' + '=' * 50, 'blue');
  if (allChecksPass) {
    log('✓ 所有检查通过，环境配置正常', 'green');
    process.exit(0);
  } else {
    log('✗ 部分检查失败，请修复上述问题', 'red');
    log('\n建议操作:', 'yellow');
    log('1. 复制 .env.example 到 .env 并配置相关参数', 'yellow');
    log('2. 确保二进制服务文件存在且有执行权限', 'yellow');
    log('3. 确保在有效的 Git 仓库中运行', 'yellow');
    log('4. 运行 pnpm install 安装依赖', 'yellow');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
