{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    // Paths configuration for shared modules
    "paths": {
      "@deepsearch/shared": ["../packages/shared/src"]
    },
    "baseUrl": "."
  },
  "include": ["src"],
  // Reference to the shared package
  "references": [{ "path": "../packages/shared" }]
}
