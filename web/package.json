{"name": "@deepsearch/web", "version": "0.1.0", "private": true, "description": "Deepsearch web 前端 (React)", "scripts": {"prepare": "echo 'web package prepare'", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@deepsearch/shared": "workspace:*", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "rimraf": "^5.0.0", "typescript": "^5.0.0", "vite": "^5.0.0"}}