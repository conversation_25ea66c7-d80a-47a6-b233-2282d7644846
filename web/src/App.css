:root {
  --background-primary: #1e1e1e;
  --background-secondary: #252526;
  --background-tertiary: #333333;
  --text-primary: #e8e8e8;
  --text-secondary: #a7a7a7;
  --accent-color: #0e639c;
  --accent-hover: #1177bb;
  --border-color: #3c3c3c;
  --user-message-bg: #2c2c2c;
  --assistant-message-bg: #252526;
  --input-background: #3c3c3c;
  --sidebar-width: 260px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 
    'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-primary);
  color: var(--text-primary);
  line-height: 1.5;
}

.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  flex: 0 0 var(--sidebar-width);
  background-color: var(--background-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  font-size: 1.4rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dc3545;
}

.connection-status.connected .status-dot {
  background-color: #28a745;
}

.connection-status.disconnected .status-dot {
  background-color: #dc3545;
}

.new-chat-btn {
  margin: 16px;
  padding: 10px;
  background-color: var(--accent-color);
  color: var(--text-primary);
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-btn:hover {
  background-color: var(--accent-hover);
}

.session-list {
  overflow-y: auto;
  flex: 1;
}

.session-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
}

.session-item:hover {
  background-color: var(--background-tertiary);
}

.session-item.active {
  background-color: var(--background-tertiary);
  border-left: 3px solid var(--accent-color);
}

.session-name {
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.fallback-messages {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-container h1 {
  font-size: 2rem;
  margin-bottom: 16px;
  font-weight: 500;
}

.welcome-container p {
  color: var(--text-secondary);
  max-width: 600px;
}

.message {
  margin-bottom: 20px;
  max-width: 90%;
}

.user-message {
  margin-left: auto;
}

.assistant-message {
  margin-right: auto;
}

.message-content {
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1.6;
}

.user-message .message-content {
  background-color: var(--accent-color);
  color: white;
}

.assistant-message .message-content {
  background-color: var(--assistant-message-bg);
  border: 1px solid var(--border-color);
}

.input-container {
  display: flex;
  padding: 16px;
  background-color: var(--background-secondary);
  border-top: 1px solid var(--border-color);
}

.question-input {
  flex: 1;
  padding: 12px 16px;
  background-color: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 1rem;
  resize: none;
  min-height: 48px;
  max-height: 150px;
  margin-right: 12px;
}

.question-input:focus {
  outline: 1px solid var(--accent-color);
}

.question-input::placeholder {
  color: var(--text-secondary);
}

.send-button {
  padding: 12px 20px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
  background-color: var(--accent-hover);
}

.send-button:disabled {
  background-color: var(--background-tertiary);
  cursor: not-allowed;
  color: var(--text-secondary);
}

/* 代码块样式 */
pre {
  background-color: var(--background-tertiary);
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

code {
  font-family: 'Menlo', 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .sidebar {
    flex: 0 0 auto;
    max-height: 200px;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
