import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 参考 VSCode 插件的 LocalMessage 类型
interface LocalMessage {
  ts: number;
  role?: "user";
  sessionId: string;
  chatId?: string;
  type: "ask" | "say";
  ask?: string;
  say?: string;
  text?: string;
  partial?: boolean;
  lastCheckpointHash?: string;
  isCheckpointCheckedOut?: boolean;
  conversationHistoryIndex?: number;
  conversationHistoryDeletedRange?: [number, number];
}

interface MessageRendererProps {
  localMessage: LocalMessage;
  isStreaming?: boolean;
}

// 参考 VSCode 插件的工具解析逻辑
const parseToolMessage = (text: string) => {
  try {
    return JSON.parse(text);
  } catch {
    return null;
  }
};

// 参考 VSCode 插件的消息类型渲染逻辑
export const MessageRenderer: React.FC<MessageRendererProps> = ({ localMessage, isStreaming }) => {
  const { type, ask, say, text, partial } = localMessage;

  // 处理工具消息
  if (ask === "tool" || say === "tool") {
    const tool = parseToolMessage(text || "{}");
    if (tool) {
      return (
        <div className="tool-message">
          <div className="tool-header">
            <span className="tool-name">{tool.tool}</span>
            {tool.path && <span className="tool-path">{tool.path}</span>}
          </div>
          {tool.content && (
            <div className="tool-content">
              <pre>{tool.content}</pre>
            </div>
          )}
          {tool.diff && (
            <div className="tool-diff">
              <pre>{tool.diff}</pre>
            </div>
          )}
        </div>
      );
    }
  }

  // 处理命令消息
  if (ask === "command" || say === "command") {
    return (
      <div className="command-message">
        <div className="command-header">
          <span className="command-icon">$</span>
          <span>命令执行</span>
        </div>
        <div className="command-content">
          <pre>{text}</pre>
        </div>
      </div>
    );
  }

  // 处理不同类型的 say 消息
  if (type === "say") {
    switch (say) {
      case "completion_result":
        return (
          <div className="completion-message">
            <div className="message-header">
              <span className="completion-icon">✓</span>
              <span>完成</span>
            </div>
            <div className="message-content">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {text || ""}
              </ReactMarkdown>
            </div>
          </div>
        );

      case "error":
        return (
          <div className="error-message">
            <div className="message-header">
              <span className="error-icon">⚠</span>
              <span>错误</span>
            </div>
            <div className="message-content">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {text || ""}
              </ReactMarkdown>
            </div>
          </div>
        );

      case "api_req_started":
        return (
          <div className="api-message">
            <div className="message-header">
              <span className="api-icon">🔄</span>
              <span>API 请求开始</span>
              {isStreaming && <span className="streaming-indicator">...</span>}
            </div>
          </div>
        );

      case "api_req_finished":
        return (
          <div className="api-message">
            <div className="message-header">
              <span className="api-icon">✓</span>
              <span>API 请求完成</span>
            </div>
          </div>
        );

      case "api_req_failed":
        return (
          <div className="api-message error">
            <div className="message-header">
              <span className="api-icon">❌</span>
              <span>API 请求失败</span>
            </div>
            {text && (
              <div className="message-content">
                <pre>{text}</pre>
              </div>
            )}
          </div>
        );

      case "text":
      case "task":
      default:
        return (
          <div className="text-message">
            <div className="message-content">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {text || ""}
              </ReactMarkdown>
              {partial && <span className="partial-indicator">▋</span>}
            </div>
          </div>
        );
    }
  }

  // 处理 ask 消息
  if (type === "ask") {
    return (
      <div className="ask-message">
        <div className="message-header">
          <span className="ask-icon">❓</span>
          <span>需要确认</span>
        </div>
        <div className="message-content">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {text || ""}
          </ReactMarkdown>
        </div>
      </div>
    );
  }

  // 默认渲染
  return (
    <div className="default-message">
      <div className="message-content">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {text || ""}
        </ReactMarkdown>
        {partial && <span className="partial-indicator">▋</span>}
      </div>
    </div>
  );
};
