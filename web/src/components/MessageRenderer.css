/* 消息渲染器样式 - 参考 VSCode 插件的设计 */

.tool-message {
  border: 1px solid #3794ff;
  border-radius: 8px;
  margin: 8px 0;
  background: rgba(55, 148, 255, 0.1);
}

.tool-header {
  padding: 8px 12px;
  background: rgba(55, 148, 255, 0.2);
  border-bottom: 1px solid #3794ff;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.tool-name {
  color: #3794ff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.tool-path {
  color: #666;
  font-size: 12px;
}

.tool-content,
.tool-diff {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.tool-content pre,
.tool-diff pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.command-message {
  border: 1px solid #28a745;
  border-radius: 8px;
  margin: 8px 0;
  background: rgba(40, 167, 69, 0.1);
}

.command-header {
  padding: 8px 12px;
  background: rgba(40, 167, 69, 0.2);
  border-bottom: 1px solid #28a745;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.command-icon {
  color: #28a745;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: bold;
}

.command-content {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.command-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #28a745;
}

.completion-message {
  border: 1px solid #28a745;
  border-radius: 8px;
  margin: 8px 0;
  background: rgba(40, 167, 69, 0.05);
}

.error-message {
  border: 1px solid #dc3545;
  border-radius: 8px;
  margin: 8px 0;
  background: rgba(220, 53, 69, 0.05);
}

.api-message {
  border: 1px solid #6c757d;
  border-radius: 8px;
  margin: 8px 0;
  background: rgba(108, 117, 125, 0.05);
}

.api-message.error {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.message-header {
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid currentColor;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.completion-icon {
  color: #28a745;
}

.error-icon {
  color: #dc3545;
}

.api-icon {
  color: #6c757d;
}

.ask-icon {
  color: #ffc107;
}

.streaming-indicator {
  color: #3794ff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.message-content {
  padding: 12px;
  line-height: 1.6;
}

.message-content pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.message-content code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.text-message,
.default-message,
.ask-message {
  margin: 8px 0;
}

.partial-indicator {
  color: #3794ff;
  animation: blink 1s infinite;
  font-weight: bold;
  margin-left: 4px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Markdown 样式优化 */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.message-content p {
  margin: 8px 0;
}

.message-content ul,
.message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-content li {
  margin: 4px 0;
}

.message-content blockquote {
  border-left: 4px solid #3794ff;
  margin: 16px 0;
  padding: 8px 16px;
  background: rgba(55, 148, 255, 0.05);
  font-style: italic;
}

.message-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.message-content th,
.message-content td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.message-content th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.message-content a {
  color: #3794ff;
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}
