/* 对话渲染器样式 - 参考 VSCode 插件和现代聊天界面设计 */

.conversation-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
  max-width: 100%;
}

.conversation-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.welcome-container {
  text-align: center;
  color: #666;
}

.welcome-container h1 {
  font-size: 32px;
  font-weight: 300;
  margin-bottom: 16px;
  color: #333;
}

.welcome-container p {
  font-size: 16px;
  opacity: 0.8;
}

.conversation {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.conversation.last-conversation {
  margin-bottom: 40px;
}

.human-message,
.assistant-messages {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.human-message {
  justify-content: flex-end;
}

.assistant-messages {
  justify-content: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
  margin-top: 4px;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assistant-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-bubble {
  max-width: 70%;
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.assistant-bubble {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 4px;
}

.message-content {
  line-height: 1.5;
  margin-bottom: 4px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
  margin-top: 4px;
}

.user-bubble .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.assistant-bubble .message-time {
  color: #6c757d;
}

.task-row {
  margin: 8px 0;
}

.task-row:first-child {
  margin-top: 0;
}

.task-row:last-child {
  margin-bottom: 0;
}

.streaming-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 0;
  border-top: 1px solid #e9ecef;
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #3794ff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.status-text {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .conversation-container {
    padding: 12px;
    gap: 16px;
  }
  
  .message-bubble {
    max-width: 85%;
  }
  
  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .user-icon,
  .assistant-icon {
    width: 28px;
    height: 28px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .welcome-container h1 {
    color: #e9ecef;
  }
  
  .welcome-container {
    color: #adb5bd;
  }
  
  .assistant-bubble {
    background: #343a40;
    color: #e9ecef;
    border-color: #495057;
  }
  
  .assistant-bubble .message-time {
    color: #adb5bd;
  }
  
  .streaming-status {
    border-top-color: #495057;
  }
  
  .status-text {
    color: #adb5bd;
  }
}

/* 滚动优化 */
.conversation-container {
  scroll-behavior: smooth;
}

/* 消息动画 */
.conversation {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
