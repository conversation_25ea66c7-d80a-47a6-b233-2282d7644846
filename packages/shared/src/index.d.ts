export interface Message {
    id: string;
    content: string;
    role: 'user' | 'assistant' | 'system';
    createdAt: number;
}
export interface SessionConfig {
    model?: string;
    temperature?: number;
    contextItems?: string[];
    systemPrompt?: string;
}
export interface Session {
    id: string;
    name?: string;
    messages: Message[];
    config: SessionConfig;
    createdAt: number;
    updatedAt: number;
}
export interface SessionSummary {
    id: string;
    name: string;
    createdAt: number;
    updatedAt: number;
    messageCount: number;
}
export declare namespace ApiRequests {
    interface Question {
        question: string;
    }
    interface CreateSession {
        name?: string;
        model?: string;
        systemPrompt?: string;
    }
    interface SendMessage {
        content: string;
    }
}
export declare namespace ApiResponses {
    interface Answer {
        answer: string;
    }
    interface SessionCreated {
        id: string;
        name: string;
        createdAt: number;
    }
    interface MessageResponse {
        id: string;
        content: string;
        role: string;
        createdAt: number;
    }
}
export declare const formatDate: (timestamp: number) => string;
export declare const truncateText: (text: string, maxLength?: number) => string;
