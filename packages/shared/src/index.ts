/**
 * DeepSearch 共享类型和工具函数
 */

// 消息类型定义
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  createdAt: number;
}

// 会话配置
export interface SessionConfig {
  model?: string;
  temperature?: number;
  contextItems?: string[];
  systemPrompt?: string;
}

// 会话数据
export interface Session {
  id: string;
  name?: string;
  messages: Message[];
  config: SessionConfig;
  createdAt: number;
  updatedAt: number;
}

// 会话摘要 (用于列表显示)
export interface SessionSummary {
  id: string;
  name: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
}

// API 请求类型
export namespace ApiRequests {
  // 问题请求接口
  export interface Question {
    question: string;
  }
  
  // 会话创建请求
  export interface CreateSession {
    name?: string;
    model?: string;
    systemPrompt?: string;
  }
  
  // 消息发送请求
  export interface SendMessage {
    content: string;
  }
}

// API 响应类型
export namespace ApiResponses {
  // 问题回答响应
  export interface Answer {
    answer: string;
  }
  
  // 创建会话响应
  export interface SessionCreated {
    id: string;
    name: string;
    createdAt: number;
  }
  
  // 消息回复响应
  export interface MessageResponse {
    id: string;
    content: string;
    role: string;
    createdAt: number;
  }
}

// 工具函数
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 搜索相关类型
export interface SearchRequest {
  query: string;
  limit?: number;
}

export interface SearchResult {
  file: string;
  content: string;
  line: number;
  score: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
}

// 索引状态类型
export interface IndexState {
  indexed: boolean;
  indexing: boolean;
  indexingProgress: number;
  indexingMessage: string;
  lastBuildTime: string;
  pauseIndexManual: boolean;
  status: 'indexing' | 'indexed' | 'paused';
}

// 代理配置类型
export interface AgentConfig {
  repoPath?: string;
  enableRepoIndex?: boolean;
  maxIndexSpace?: number;
  proxyUrl?: string;
  agentPreference?: string;
  userName?: string;
}

// 项目信息类型
export interface ProjectInfo {
  name: string;
  gitRepo: string;
  gitRemote: string;
  currentBranchName: string;
  username: string;
  userEmail: string;
  repoPath: string;
  currentCommit: string;
}

// 服务状态类型
export interface ServiceStatus {
  status: 'ok' | 'error';
  message?: string;
  timestamp: string;
}

// 扩展 API 请求类型
export namespace ApiRequests {
  // 问题请求接口
  export interface Question {
    question: string;
  }

  // 会话创建请求
  export interface CreateSession {
    name?: string;
    model?: string;
    systemPrompt?: string;
  }

  // 消息发送请求
  export interface SendMessage {
    content: string;
  }

  // 搜索请求
  export interface Search {
    query: string;
    limit?: number;
  }
}

// 扩展 API 响应类型
export namespace ApiResponses {
  // 问题回答响应
  export interface Answer {
    answer: string;
  }

  // 创建会话响应
  export interface SessionCreated {
    id: string;
    name: string;
    createdAt: number;
  }

  // 消息回复响应
  export interface MessageResponse {
    id: string;
    content: string;
    role: string;
    createdAt: number;
  }

  // 搜索响应
  export interface Search {
    results: SearchResult[];
    total: number;
    query: string;
  }

  // 索引状态响应
  export interface IndexStatus extends IndexState {}

  // 配置响应
  export interface Config extends AgentConfig {}

  // 服务状态响应
  export interface Status extends ServiceStatus {}
}
